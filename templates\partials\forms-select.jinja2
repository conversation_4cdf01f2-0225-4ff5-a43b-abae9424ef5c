<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 64px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me select {
        all: unset;
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        font-weight: 400;
        color: var(--color-text-black);
        height: 24px;
        border: 0;
        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M7 10l5 5 5-5' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat right var(--color-background-bright);
        background-size: 26px 24px;

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-24px);
            }
        }

        &:valid:not([value=""]) {
            border-bottom: 1px solid var(--color-selected-green);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-24px);
            }
        }

        &:disabled {
            color: var(--color-disabled);
            border-bottom: 1px solid var(--color-disabled);
        }
      }

      me .input-label.float-label {
        font-family: 'Noto Serif', serif;
        font-style: italic;
        font-size: 15px;
        color: var(--color-text-dark);
        transform: translateY(-24px);
      }
      select:disabled + .input-label.float-label {
        color: var(--color-disabled);
      }

      me .input-label,
      .input-label {
          pointer-events: none;
      }
  </style>

  <select class="custom-float-select" name="{{ namealwayschange }}" id="{{ namealwayschange }}" onchange="this.setAttribute('value', this.value);" {% if required is not defined or required %} required{% endif %} >
    <option value=""></option>
    {% for option in optionslist %}
      {% if option is mapping %}
        <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
      {% else %}
        <option value="{{ option }}">{{ option }}</option>
      {% endif %}
    {% endfor %}
  </select>
  <label class="input-label custom-float-label">
    <style>
        me {
            color: var(--color-text-black);
            position: absolute;
            transition: .15s ease;
            margin-bottom: 6px;
        }
        select:disabled + .input-label {
            color: var(--color-disabled);
        }
        select:disabled + .input-label.float-label {
            color: var(--color-disabled);
        }
    </style>
    {{ label | safe }}
  </label>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
  // For all custom-float-select elements on the page
  document.querySelectorAll('.custom-float-select').forEach(function(select) {
    const label = select.nextElementSibling && select.nextElementSibling.classList.contains('custom-float-label') ? select.nextElementSibling : null;
    function updateLabel() {
      if (select && label) {
        if (select.value && select.value !== "") {
          label.classList.add('float-label');
        } else {
          label.classList.remove('float-label');
        }
      }
    }
    // Set value attribute for CSS compatibility
    select.setAttribute('value', select.value);
    updateLabel();
    select.addEventListener('change', function() {
      select.setAttribute('value', select.value);
      updateLabel();
    });
    // Also update label on page load in case value is set by browser autofill or server
    updateLabel();
  });
});
</script>

